---
import type { NavigationItem } from "@/data/portfolio";
import SkipToContentButton from "@/components/ui/SkipToContentButton.vue";
import MobileNavigation from "@/components/ui/MobileNavigation.vue";

interface Props {
    title: string;
    navigation: NavigationItem[];
    skipToContentText: string;
}

const { title, navigation, skipToContentText } = Astro.props;
---

<SkipToContentButton text={skipToContentText} targetId="main" client:load />

<header
    class="sticky top-0 z-40 backdrop-blur supports-[backdrop-filter]:bg-white/70 bg-white/90 border-b border-slate-200"
>
    <nav class="mx-auto max-w-6xl px-4 sm:px-6 py-3 flex items-center justify-between" aria-label="Main navigation">
        <!-- Responsive Brand Name -->
        <div class="text-sky-900 font-bold tracking-tight">
            <!-- Full brand name on larger screens -->
            <span class="hidden sm:inline">{title}</span>
            <!-- Shortened brand name on mobile -->
            <span class="sm:hidden">Portfolio</span>
        </div>

        <!-- Desktop Navigation -->
        <ul class="hidden lg:flex gap-6 text-sm" role="list">
            {
                navigation.map((item) => (
                    <li>
                        <a
                            href={item.href}
                            class="hover:text-sky-800 focus:outline-none focus:ring-2 focus:ring-sky-600 rounded px-2 py-1 transition-colors duration-200"
                        >
                            {item.label}
                        </a>
                    </li>
                ))
            }
        </ul>

        <!-- Mobile Navigation -->
        <MobileNavigation navigation={navigation} client:load />
    </nav>
</header>
