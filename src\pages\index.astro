---
import Layout from "@/layouts/main.astro";
import Header from "@/components/layout/Header.astro";
import Hero from "@/components/sections/Hero.astro";
import PublicationsWithFilter from "@/components/publications/PublicationsWithFilter.vue";
import Experience from "@/components/sections/Experience.astro";
import SoftwareScreens from "@/components/sections/SoftwareScreens.astro";
import Footer from "@/components/layout/Footer.astro";
import {
    personalInfo,
    publications,
    employment,
    education,
    certifications,
    softwareScreens,
    navigation,
    sectionTitles,
    siteInfo,
} from "@/data/portfolio";
---

<Layout title={siteInfo.title} bodyClass="bg-[#f3f6fb] text-slate-800">
    <Header title={siteInfo.title} navigation={navigation} skipToContentText={siteInfo.skipToContentText} />

    <main id="main" class="mx-auto max-w-6xl px-4 sm:px-6">
        <Hero sectionTitle={sectionTitles.researchFields} personalInfo={personalInfo} />

        <PublicationsWithFilter sectionTitle={sectionTitles.publications} publications={publications} client:load />

        <Experience
            sectionTitle={sectionTitles.professionalExperience}
            employment={employment}
            education={education}
            certifications={certifications}
        />

        <SoftwareScreens sectionTitle={sectionTitles.softwareScreens} softwareScreens={softwareScreens} />
    </main>

    <Footer personalName={personalInfo.name} footerText={siteInfo.footerText} />
</Layout>
